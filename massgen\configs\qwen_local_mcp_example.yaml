# MassGen Configuration: Gemini with MCP Integration
# Usage:
#   uv run python -m massgen.cli --config configs/qwen_local_mcp_example.yaml "whats the weather of Tokyo"
agents:
  - id: "qwen3-4b-mcp_weather" # Cerebras AI  
    backend:
      type: "lmstudio"
      model: "qwen/qwen3-4b-2507" 
      mcp_servers:
        - name: "weather"
          type: "stdio"
          command: "npx"
          args: ["-y", "@fak111/weather-mcp"]
    system_message: |
      You are an AI assistant with access to weather information through MCP (Model Context Protocol) integration.

      Weather tools are available via MCP sessions and will be called automatically by the system when needed.
      Do not output tool call syntax or function declarations. Focus on answering the user's question clearly.

      When users ask about weather conditions, forecasts, or alerts, include the location and time frame in your
      response and provide concise, up-to-date information using the integrated tools.
ui:
  display_type: "simple"
  logging_enabled: true