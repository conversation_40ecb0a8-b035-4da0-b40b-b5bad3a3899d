# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

MassGen is a multi-agent scaling system for GenAI that enables collaborative problem-solving through multiple AI agents working in parallel. The system orchestrates agents from different model providers (Claude, Gemini, GPT, Grok, etc.) who share insights and build consensus to deliver comprehensive results.

## Development Environment

### Package Management
- **Primary tool**: `uv` (Python package manager)
- **Virtual environment**: `uv venv` to create, `source .venv/bin/activate` (Linux/Mac) or `.venv\Scripts\activate` (Windows)
- **Dependencies**: Defined in `pyproject.toml` and `requirements.txt`

### Common Commands

#### Running MassGen
```bash
# Single agent with quick setup
uv run python -m massgen.cli --model claude-3-5-sonnet-latest "your question"

# Multi-agent from configuration
uv run python -m massgen.cli --config massgen/configs/three_agents_default.yaml "your question"

# Interactive mode
uv run python -m massgen.cli --config massgen/configs/three_agents_default.yaml
```

#### Development Tasks
```bash
# Run tests
uv run pytest

# Run specific test
uv run pytest massgen/tests/test_integration_simple.py

# Run tests with coverage
uv run pytest --cov=massgen

# Code formatting
uv run black massgen/
uv run isort massgen/

# Type checking
uv run mypy massgen/

# Linting
uv run flake8 massgen/

# Security scanning
uv run bandit -r massgen/
```

#### MCP Server Testing
```bash
# Test HTTP MCP server
uv run python massgen/tests/test_http_mcp_server.py

# Test stdio MCP server
uv run python massgen/tests/mcp_test_server.py
```

## Architecture Overview

### Core Components

1. **Backend System** (`massgen/backend/`): Abstract interface for different LLM providers
   - `base.py`: Base `LLMBackend` class and `StreamChunk` data structure
   - Provider implementations: `claude.py`, `gemini.py`, `openai.py`, `grok.py`, etc.
   - Special backends: `claude_code.py` (Claude Code SDK), `lmstudio.py` (local models)

2. **Agent System** (`massgen/chat_agent.py`, `massgen/agent_config.py`): 
   - `ChatAgent`: Abstract interface for both single agents and orchestrator
   - `ConfigurableAgent`: Single agent with backend and configuration
   - `SingleAgent`: Simplified single agent interface

3. **Orchestrator** (`massgen/orchestrator.py`): Multi-agent coordination system
   - Manages parallel agent execution
   - Implements consensus building through voting
   - Handles agent restart and refinement cycles

4. **Frontend/UI** (`massgen/frontend/`): Display and coordination interfaces
   - `coordination_ui.py`: Main UI coordinator
   - `displays/`: Different display types (rich terminal, simple, terminal)

5. **MCP Integration** (`massgen/mcp_tools/`): Model Context Protocol support
   - `client.py`: MCP client implementation
   - `filesystem_manager.py`: Unified filesystem operations
   - `circuit_breaker.py`: Error handling and retry logic

### Key Design Patterns

- **Async Streaming**: All backends implement async streaming via `AsyncGenerator[StreamChunk, None]`
- **Configuration-Driven**: Agent behavior defined through YAML/JSON configuration files
- **Plugin Architecture**: New backends and tools can be added without core changes
- **Unified Interface**: Single agents and multi-agent systems share the same `ChatAgent` interface

### MCP (Model Context Protocol) Architecture

MassGen provides comprehensive MCP support across multiple backends:

- **Gemini Backend**: Full MCP support with multi-server capability
- **OpenAI Backend**: Complete MCP integration (v0.0.17+)
- **Claude Code Backend**: Native MCP support with Claude Code SDK
- **Chat Completions Backend**: MCP support for compatible providers

MCP servers are configured per-agent in YAML files and support:
- **stdio transport**: Standard input/output communication
- **streamable-http transport**: HTTP-based communication with SSE
- **Tool discovery**: Automatic tool discovery and execution
- **Security framework**: Sandboxed execution and validation

## Configuration System

### Configuration Structure
Configuration files use YAML format with the following main sections:

```yaml
# Single agent
agent:
  id: "agent_name"
  backend:
    type: "gemini|openai|claude|grok|claude_code|lmstudio|..."
    model: "model_name"
    # Backend-specific settings
  system_message: "custom prompt"

# Multi-agent
agents:
  - id: "agent1"
    backend: {type: "...", model: "..."}
    system_message: "..."
  - id: "agent2"
    backend: {type: "...", model: "..."}

# MCP servers (backend-specific)
mcp_servers:
  server_name:
    type: "stdio|streamable-http"
    command: "npx"
    args: ["-y", "package-name"]
    url: "http://localhost:port"  # for streamable-http

# UI settings
ui:
  display_type: "rich_terminal|terminal|simple"
  logging_enabled: true

# Timeout settings
timeout_settings:
  orchestrator_timeout_seconds: 30
```

### Configuration Files Location
- Example configurations: `massgen/configs/`
- Common patterns: `*_agent.yaml`, `*_mcp_*.yaml`, multi-agent setups

## Backend Development

### Adding New Backends
1. Extend `LLMBackend` from `massgen/backend/base.py`
2. Implement required async methods:
   - `chat_streaming()`: Main streaming interface
   - `get_token_usage()`: Token tracking
   - `supports_tools()`: Tool capability indication
3. Add backend to `massgen/utils.py` model mapping
4. Update CLI argument parsing in `massgen/cli.py`

### Backend Configuration
Each backend accepts configuration through the `BackendConfig` dataclass:
- `api_key`: API key (uses environment variables by default)
- `base_url`: Custom API endpoint
- `model`: Model name
- `temperature`: Response creativity (0.0-1.0)
- `max_tokens`: Maximum response length
- Backend-specific settings (e.g., `enable_web_search` for Gemini)

## Testing Strategy

### Test Structure
- `massgen/tests/`: All test files
- Integration tests: Test full agent workflows
- Backend tests: Test individual backend implementations
- MCP tests: Test MCP server integration

### Running Tests
```bash
# All tests
uv run pytest

# Integration tests only
uv run pytest -m integration

# Backend-specific tests
uv run pytest massgen/tests/test_claude_backend.py
uv run pytest massgen/tests/test_gemini_backend.py

# With debug output
uv run pytest -v -s
```

## Logging and Debugging

### Logging System
- **Configuration**: `massgen/logger_config.py`
- **Session-based**: Logs organized by timestamp in `massgen_logs/log_{timestamp}/`
- **Debug mode**: Use `--debug` flag for verbose logging
- **Structure**: 
  - `agent_outputs/`: Raw agent responses
  - `final_workspace/`: Final presentations
  - `massgen_debug.log`: Detailed debug information

### Debug Mode
Enable debug mode for comprehensive logging:
```bash
uv run python -m massgen.cli --debug --config config.yaml "your question"
```

## Environment Setup

### Required Environment Variables
Create `.env` file from `.env.example`:
```bash
# Copy template
cp .env.example .env

# Add your API keys
ANTHROPIC_API_KEY=your_claude_key
OPENAI_API_KEY=your_openai_key
GOOGLE_API_KEY=your_gemini_key
XAI_API_KEY=your_grok_key
AZURE_OPENAI_API_KEY=your_azure_key
# Add other required keys based on backends used
```

### MCP Server Dependencies
Some MCP servers require additional setup:
- **Brave Search**: `BRAVE_API_KEY`
- **Discord**: Discord bot token in server configuration
- **Playwright**: Node.js and Playwright MCP package

## File Structure Conventions

### Core Directories
- `massgen/`: Main package code
  - `backend/`: LLM provider implementations
  - `frontend/`: UI and display components
  - `mcp_tools/`: MCP integration code
  - `configs/`: Configuration file examples
  - `tests/`: Test suite

### Configuration Files
- Named descriptively: `three_agents_default.yaml`, `claude_code_mcp_example.yaml`
- Include comments explaining configuration options
- Use consistent structure across files

## Development Workflow

1. **Feature Development**:
   - Create feature branch from main
   - Implement changes with tests
   - Run full test suite
   - Update documentation if needed

2. **Backend Integration**:
   - Start with `massgen/backend/base.py` interface
   - Implement streaming response handling
   - Add configuration support
   - Write comprehensive tests

3. **MCP Server Integration**:
   - Test with existing MCP servers first
   - Use `massgen/tests/mcp_test_server.py` for stdio testing
   - Use `massgen/tests/test_http_mcp_server.py` for HTTP testing
   - Follow security best practices in `massgen/mcp_tools/security.py`

## Important Notes

- **Python Version**: Requires Python 3.10+
- **Async/Await**: Heavy use of async programming patterns
- **Streaming First**: All backends must implement streaming responses
- **Configuration Driven**: Behavior controlled through YAML files, not code changes
- **MCP Centric**: Tool integration primarily through MCP servers
- **Cross-Platform**: Windows support with proper path handling