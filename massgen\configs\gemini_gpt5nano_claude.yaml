# MassGen Three Agent Configuration
# Gemini-2.5-flash, GPT-5-nano, and Claude-3.5-Haiku with builtin tools enabled
# uv run python -m massgen.cli --config massgen/configs/gemini_gpt5nano_claude.yaml " explain quantum computing in simple terms"

agents:
  - id: "gemini2.5flash"
    backend:
      type: "gemini"
      model: "gemini-2.5-flash"
      # enable_code_execution: true  # Disabled by default - can preempt web search, resulting in weaker search capability
    # system_message: "You are a helpful AI assistant with web search and code execution capabilities. For any question involving current events, recent information, or real-time data, ALWAYS use web search first."

  - id: "gpt-5-nano"
    backend:
      type: "openai"
      model: "gpt-5-nano"
    # system_message: "You are a helpful AI assistant with web search and code execution capabilities. For any question involving current events, recent information, or real-time data, ALWAYS use web search first."

  - id: "claude-3-5-haiku"
    backend:
      type: "claude"
      model: "claude-sonnet-4-20250514"
    # system_message: "You are a helpful AI assistant with web search capabilities. For any question involving current events, recent information, or real-time data, ALWAYS use web search first."

ui:
  display_type: "rich_terminal"
  logging_enabled: true
