        <!-- Slide 12: Context Sharing Solution -->
        <div class="slide">
            <div class="icon">✅</div>
            <h2>Our Context Sharing Solution</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; align-items: center;">
                <div>
                    <ul style="font-size: 1.4em; line-height: 1.8;">
                        <li><strong>📸 Workspace Snapshots:</strong> Orchestrator captures agent workspaces after each round</li>
                        <li><strong>📁 Temporary Directories:</strong> Each agent gets a clean temp workspace with all snapshots</li>
                        <li><strong>🎭 Anonymous Mapping:</strong> agent1/, agent2/ folders preserve anonymity</li>
                        <li><strong>🔒 Clean Separation:</strong> Read from temp dir, write to permanent workspace</li>
                        <li><strong>🔄 Context Preservation:</strong> Snapshots linked to coordination rounds</li>
                    </ul>
                </div>
                <div style="text-align: center;">
                    <svg viewBox="0 0 600 500" xmlns="http://www.w3.org/2000/svg" style="width: 100%; max-width: 550px; height: auto;">
                        <!-- Orchestrator -->
                        <rect x="250" y="20" width="100" height="40" rx="8" fill="#9b59b6" stroke="#8e44ad" stroke-width="3"/>
                        <text x="300" y="45" text-anchor="middle" font-family="Arial" font-size="16" font-weight="bold" fill="white">Orchestrator</text>
                        
                        <!-- Snapshot Storage -->
                        <rect x="200" y="80" width="200" height="80" rx="8" fill="#e8f5e8" stroke="#27ae60" stroke-width="2"/>
                        <text x="300" y="105" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="#27ae60">Snapshot Storage</text>
                        <rect x="220" y="115" width="60" height="30" rx="4" fill="#27ae60"/>
                        <text x="250" y="135" text-anchor="middle" font-family="Arial" font-size="11" fill="white" font-weight="bold">agent1/</text>
                        <rect x="290" y="115" width="60" height="30" rx="4" fill="#27ae60"/>
                        <text x="320" y="135" text-anchor="middle" font-family="Arial" font-size="11" fill="white" font-weight="bold">agent2/</text>
                        
                        <!-- Agents with permanent workspaces -->
                        <rect x="50" y="200" width="120" height="80" rx="8" fill="#3498db" stroke="#2980b9" stroke-width="2"/>
                        <text x="110" y="225" text-anchor="middle" font-family="Arial" font-size="14" fill="white" font-weight="bold">Agent 1</text>
                        <rect x="70" y="235" width="80" height="25" rx="4" fill="#2980b9"/>
                        <text x="110" y="252" text-anchor="middle" font-family="Arial" font-size="10" fill="white">Permanent WS</text>
                        <rect x="70" y="260" width="80" height="15" rx="2" fill="#1abc9c"/>
                        <text x="110" y="270" text-anchor="middle" font-family="Arial" font-size="9" fill="white">Temp Context</text>
                        
                        <rect x="430" y="200" width="120" height="80" rx="8" fill="#3498db" stroke="#2980b9" stroke-width="2"/>
                        <text x="490" y="225" text-anchor="middle" font-family="Arial" font-size="14" fill="white" font-weight="bold">Agent 2</text>
                        <rect x="450" y="235" width="80" height="25" rx="4" fill="#2980b9"/>
                        <text x="490" y="252" text-anchor="middle" font-family="Arial" font-size="10" fill="white">Permanent WS</text>
                        <rect x="450" y="260" width="80" height="15" rx="2" fill="#1abc9c"/>
                        <text x="490" y="270" text-anchor="middle" font-family="Arial" font-size="9" fill="white">Temp Context</text>
                        
                        <!-- Snapshot arrows -->
                        <line x1="150" y1="200" x2="250" y2="160" stroke="#27ae60" stroke-width="3" marker-end="url(#arrowhead-green)"/>
                        <line x1="450" y1="200" x2="350" y2="160" stroke="#27ae60" stroke-width="3" marker-end="url(#arrowhead-green)"/>
                        
                        <!-- Context sharing arrows - from snapshot storage to temp context -->
                        <path d="M 250 160 Q 200 200 150 260" stroke="#1abc9c" stroke-width="2" fill="none" stroke-dasharray="5,5" marker-end="url(#arrowhead-teal)"/>
                        <path d="M 350 160 Q 400 200 450 260" stroke="#1abc9c" stroke-width="2" fill="none" stroke-dasharray="5,5" marker-end="url(#arrowhead-teal)"/>
                        <text x="200" y="210" text-anchor="middle" font-family="Arial" font-size="10" fill="#1abc9c">context</text>
                        <text x="400" y="210" text-anchor="middle" font-family="Arial" font-size="10" fill="#1abc9c">sharing</text>
                        
                        <!-- Agent 1 Temp workspace -->
                        <rect x="150" y="320" width="120" height="80" rx="8" fill="#ecf0f1" stroke="#3498db" stroke-width="2"/>
                        <text x="210" y="340" text-anchor="middle" font-family="Arial" font-size="11" font-weight="bold" fill="#2c3e50">Agent 1 Temp Copy</text>
                        <rect x="170" y="350" width="35" height="15" rx="2" fill="#e74c3c"/>
                        <text x="187" y="360" text-anchor="middle" font-family="Arial" font-size="8" fill="white">agent1/</text>
                        <rect x="215" y="350" width="35" height="15" rx="2" fill="#3498db"/>
                        <text x="232" y="360" text-anchor="middle" font-family="Arial" font-size="8" fill="white">agent2/</text>
                        <text x="210" y="380" text-anchor="middle" font-family="Arial" font-size="9" fill="#7f8c8d">Own temp copy</text>
                        
                        <!-- Agent 2 Temp workspace -->
                        <rect x="330" y="320" width="120" height="80" rx="8" fill="#ecf0f1" stroke="#e74c3c" stroke-width="2"/>
                        <text x="390" y="340" text-anchor="middle" font-family="Arial" font-size="11" font-weight="bold" fill="#2c3e50">Agent 2 Temp Copy</text>
                        <rect x="350" y="350" width="35" height="15" rx="2" fill="#e74c3c"/>
                        <text x="367" y="360" text-anchor="middle" font-family="Arial" font-size="8" fill="white">agent1/</text>
                        <rect x="395" y="350" width="35" height="15" rx="2" fill="#3498db"/>
                        <text x="412" y="360" text-anchor="middle" font-family="Arial" font-size="8" fill="white">agent2/</text>
                        <text x="390" y="380" text-anchor="middle" font-family="Arial" font-size="9" fill="#7f8c8d">Own temp copy</text>
                        
                        <!-- Connection lines from agents to their temp workspaces -->
                        <line x1="150" y1="275" x2="210" y2="320" stroke="#1abc9c" stroke-width="1" stroke-dasharray="2,2"/>
                        <line x1="450" y1="275" x2="390" y2="320" stroke="#1abc9c" stroke-width="1" stroke-dasharray="2,2"/>
                        
                        <!-- Key benefits -->
                        <rect x="20" y="450" width="560" height="40" rx="8" fill="#d4edda" stroke="#27ae60" stroke-width="2"/>
                        <text x="300" y="475" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="#155724">✅ Safe Context Sharing + ✅ No Interference + ✅ Full Verification</text>
                        
                        <defs>
                            <marker id="arrowhead-green" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="#27ae60"/>
                            </marker>
                            <marker id="arrowhead-teal" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="#1abc9c"/>
                            </marker>
                        </defs>
                    </svg>
                </div>
            </div>
        </div>