# MassGen Configuration
# Usage:
# uv run python -m massgen.cli --config configs/gemini_mcp_filesystem_test_with_claude_code.yaml "Create a presentation that teaches a reinforcement learning algorithm and output it in LaTeX Beamer format. No figures should be added."
agents:
  - id: "gemini_agent"
    backend:
      type: "gemini"
      model: "gemini-2.5-pro"
      cwd: "workspace1"  # Working directory for file operations
      enable_web_search: true

  - id: "claude_code"
    backend:
      type: "claude_code"
      model: "claude-sonnet-4-20250514"
      cwd: "workspace2"  # Working directory for file operations

orchestrator:
    snapshot_storage: "snapshots"  # Directory to store workspace snapshots
    agent_temporary_workspace: "temp_workspaces"  # Directory for temporary agent workspaces

ui:
  display_type: "rich_terminal"
  logging_enabled: true
