# MassGen Configuration: Gemini with MCP Integration
# Usage:
#   uv run python -m massgen.cli --config configs/gemini_mcp_example.yaml "whats the weather of Tokyo"
agents:
  - id: "claude_mcp_weather"
    backend:
      type: "claude"
      model: "glm-4.5"
      mcp_servers:
        - name: "weather"
          type: "stdio"
          command: "npx"
          args: ["-y", "@fak111/weather-mcp"]
          
  - id: "gemini2.5flash_mcp_weather"
    backend:
      type: "gemini"
      model: "gemini-2.5-flash"
      mcp_servers:
        - name: "weather"
          type: "stdio"
          command: "npx"
          args: ["-y", "@fak111/weather-mcp"]
ui:
  display_type: "rich_terminal"
  logging_enabled: true