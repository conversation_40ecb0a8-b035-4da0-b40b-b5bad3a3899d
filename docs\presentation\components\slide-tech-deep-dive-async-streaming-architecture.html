        <!-- Slide 8: Tech Deep Dive - Async Streaming Architecture -->
        <div class="slide">
            <div class="icon">⚙️</div>
            <h2>Tech Deep Dive: Async Streaming & Dynamic Scheduling</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; align-items: center;">
                <div>
                    <ul style="font-size: 1.4em;">
                        <li><strong>🔄 AsyncGenerator Pattern:</strong> Real-time streaming from 5+ agents simultaneously</li>
                        <li><strong>⚡ Dynamic Task Management:</strong> Agents start/stop based on voting status</li>
                        <li><strong>🔁 Graceful Restart & Wrap-up:</strong> Dynamic wrapping-up as part of scheduling</li>
                    </ul>
                </div>
                <div style="text-align: center;">
                    <svg viewBox="0 0 500 400" xmlns="http://www.w3.org/2000/svg" style="width: 100%; max-width: 450px; height: auto;">
                        <!-- Orchestrator -->
                        <rect x="200" y="20" width="100" height="40" rx="5" fill="#74b9ff" stroke="#0984e3" stroke-width="2"/>
                        <text x="250" y="45" text-anchor="middle" font-family="Arial" font-size="16" font-weight="bold" fill="white">Orchestrator</text>
                        
                        <!-- Agent streams -->
                        <rect x="50" y="120" width="80" height="30" rx="3" fill="#fd79a8" stroke="#e84393" stroke-width="2"/>
                        <text x="90" y="140" text-anchor="middle" font-family="Arial" font-size="14" fill="white">Agent 1</text>
                        
                        <rect x="150" y="120" width="80" height="30" rx="3" fill="#fd79a8" stroke="#e84393" stroke-width="2"/>
                        <text x="190" y="140" text-anchor="middle" font-family="Arial" font-size="14" fill="white">Agent 2</text>
                        
                        <rect x="250" y="120" width="80" height="30" rx="3" fill="#fd79a8" stroke="#e84393" stroke-width="2"/>
                        <text x="290" y="140" text-anchor="middle" font-family="Arial" font-size="14" fill="white">Agent 3</text>
                        
                        <rect x="350" y="120" width="80" height="30" rx="3" fill="#fd79a8" stroke="#e84393" stroke-width="2"/>
                        <text x="390" y="140" text-anchor="middle" font-family="Arial" font-size="14" fill="white">Agent 4</text>
                        
                        <!-- Streaming arrows -->
                        <line x1="250" y1="70" x2="90" y2="110" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <line x1="250" y1="70" x2="190" y2="110" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <line x1="250" y1="70" x2="290" y2="110" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <line x1="250" y1="70" x2="390" y2="110" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
                        
                        <!-- StreamChunk types -->
                        <rect x="50" y="180" width="100" height="25" rx="3" fill="#00b894" stroke="#00cec9" stroke-width="1"/>
                        <text x="100" y="198" text-anchor="middle" font-family="Arial" font-size="13" fill="white">content</text>
                        
                        <rect x="160" y="180" width="100" height="25" rx="3" fill="#fdcb6e" stroke="#e17055" stroke-width="1"/>
                        <text x="210" y="198" text-anchor="middle" font-family="Arial" font-size="13" fill="white">tool_calls</text>
                        
                        <rect x="270" y="180" width="100" height="25" rx="3" fill="#a29bfe" stroke="#6c5ce7" stroke-width="1"/>
                        <text x="320" y="198" text-anchor="middle" font-family="Arial" font-size="13" fill="white">reasoning</text>
                        
                        <!-- Restart mechanism -->
                        <text x="250" y="240" text-anchor="middle" font-family="Arial" font-size="15" font-weight="bold" fill="#e74c3c">🔁 Restart Trigger</text>
                        <text x="250" y="260" text-anchor="middle" font-family="Arial" font-size="14" fill="#666">When Agent 2 provides new_answer</text>
                        
                        <!-- Restart arrows -->
                        <path d="M 190 280 Q 140 300 90 280" stroke="#e74c3c" stroke-width="2" fill="none" marker-end="url(#arrowhead-red)"/>
                        <path d="M 190 280 Q 240 300 290 280" stroke="#e74c3c" stroke-width="2" fill="none" marker-end="url(#arrowhead-red)"/>
                        <path d="M 190 280 Q 340 320 390 280" stroke="#e74c3c" stroke-width="2" fill="none" marker-end="url(#arrowhead-red)"/>
                        
                        <text x="90" y="310" text-anchor="middle" font-family="Arial" font-size="13" fill="#e74c3c">restarting</text>
                        <text x="290" y="310" text-anchor="middle" font-family="Arial" font-size="13" fill="#e74c3c">restarting</text>
                        <text x="390" y="340" text-anchor="middle" font-family="Arial" font-size="13" fill="#e74c3c">restarting</text>
                        
                        <!-- Arrow markers -->
                        <defs>
                            <marker id="arrowhead" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="#333"/>
                            </marker>
                            <marker id="arrowhead-red" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="#e74c3c"/>
                            </marker>
                        </defs>
                    </svg>
                    <div style="margin-top: 15px; font-size: 1em; color: #7f8c8d;">
                        <strong>Key Innovation:</strong> Dynamic coordination without deadlocks
                    </div>
                </div>
            </div>
        </div>