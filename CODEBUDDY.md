# MassGen - Multi-Agent Scaling System

This repository contains MassGen, a cutting-edge multi-agent system that leverages collaborative AI to solve complex tasks through intelligent coordination and consensus building.

## Development Commands

### Environment Setup
```bash
# Install UV package manager
pip install uv

# Create virtual environment
uv venv

# Activate virtual environment (Windows)
.venv\Scripts\activate

# Install dependencies
uv sync
```

### Running MassGen
```bash
# Run with configuration file
uv run python -m massgen.cli --config <config.yaml> "Your question here"

# Quick single agent setup
uv run python -m massgen.cli --backend claude --model claude-3-5-sonnet-latest "Your question"
uv run python -m massgen.cli --backend openai --model gpt-4o-mini "Your question"

# Interactive mode
uv run python -m massgen.cli --config three_agents_default.yaml

# Debug mode with verbose logging
uv run python -m massgen.cli --config <config.yaml> --debug "Your question"
```

### Testing
```bash
# Run tests
uv run pytest massgen/tests/

# Run specific test categories
uv run pytest -m unit massgen/tests/
uv run pytest -m integration massgen/tests/
```

### Code Quality
```bash
# Format code
uv run black massgen/
uv run isort massgen/

# Lint code
uv run flake8 massgen/
uv run mypy massgen/

# Security scan
uv run bandit -r massgen/
```

## Architecture Overview

MassGen implements a sophisticated multi-agent coordination system with the following key architectural components:

### Core Components

1. **Orchestrator (`orchestrator.py`)**: Central coordinator that manages multiple agents using a binary decision framework. Implements timeout management, workspace sharing, and consensus building.

2. **Chat Agents (`chat_agent.py`)**: Individual AI agents that can be configured with different LLM backends. Support conversation history and tool integration.

3. **Backend System (`backend/`)**: Modular LLM provider integrations supporting:
   - OpenAI (GPT-4, GPT-5 series with reasoning support)
   - Anthropic Claude (with native tools)
   - Google Gemini (with MCP integration)
   - xAI Grok (with web search)
   - Azure OpenAI
   - Local models via LM Studio
   - Claude Code SDK (with comprehensive dev tools)

4. **MCP Integration (`mcp_tools/`)**: Model Context Protocol support for external tool integration, including filesystem operations, Discord, Twitter, and web automation.

### Multi-Agent Coordination Workflow

The system uses a proven binary decision framework:

1. **Task Distribution**: Orchestrator distributes tasks to multiple agents simultaneously
2. **Parallel Processing**: Agents work independently with real-time streaming output
3. **Intelligence Sharing**: Agents share workspace snapshots and observe each other's progress
4. **Consensus Building**: Agents vote on existing answers or provide new solutions
5. **Graceful Restart**: When new answers emerge, agents gracefully restart to evaluate updated context
6. **Final Presentation**: Selected agent presents coordinated final answer

### Backend Architecture

- **Base Interface**: `LLMBackend` abstract class defines standard streaming interface
- **Filesystem Support**: Three levels - NONE, NATIVE (Claude Code), MCP (injected filesystem tools)
- **Tool Integration**: Standardized tool calling across different LLM providers
- **Streaming**: Unified `StreamChunk` format for real-time response streaming

### Configuration System

- **YAML/JSON Configs**: Flexible agent team definitions in `massgen/configs/`
- **Backend Parameters**: Model-specific settings (temperature, tokens, tools)
- **UI Settings**: Display modes (rich_terminal, terminal, simple)
- **Timeout Management**: Orchestrator-level timeout with graceful fallbacks
- **MCP Server Configuration**: External tool server definitions

## Key Development Patterns

### Adding New LLM Backends

1. Extend `LLMBackend` base class in `backend/`
2. Implement `stream_with_tools()` method
3. Add model mappings to `utils.py`
4. Create backend-specific config in `agent_config.py`
5. Add provider to CLI backend choices

### Agent Configuration

Agents support both single and multi-agent configurations:

```yaml
# Single agent
agent:
  id: "researcher"
  backend:
    type: "claude"
    model: "claude-3-5-sonnet-latest"
  system_message: "You are a research assistant"

# Multi-agent team
agents:
  - id: "researcher"
    backend: {type: "claude", model: "claude-3-5-sonnet-latest"}
  - id: "analyst" 
    backend: {type: "openai", model: "gpt-4o"}
```

### MCP Server Integration

MCP servers extend agent capabilities:

```yaml
backend:
  type: "gemini"
  mcp_servers:
    weather:
      type: "stdio"
      command: "npx"
      args: ["-y", "@fak111/weather-mcp"]
```

### Workspace Context Sharing

Claude Code agents share context through workspace snapshots:
- Orchestrator manages snapshot storage directory
- Agents copy each other's work to temporary workspaces
- Filesystem operations are tracked and logged
- Final presentations preserve complete workspace state

## Environment Variables

Required API keys (only for backends you use):

```bash
# Core providers
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_claude_key
GOOGLE_API_KEY=your_gemini_key
XAI_API_KEY=your_grok_key

# Additional providers
CEREBRAS_API_KEY=your_cerebras_key
TOGETHER_API_KEY=your_together_key
FIREWORKS_API_KEY=your_fireworks_key
GROQ_API_KEY=your_groq_key

# Azure OpenAI
AZURE_OPENAI_API_KEY=your_azure_key
AZURE_OPENAI_ENDPOINT=your_azure_endpoint

# External services (for MCP)
BRAVE_API_KEY=your_brave_search_key
```

## Logging and Debugging

MassGen provides comprehensive logging:

- **Session Logs**: Stored in `massgen_logs/log_{timestamp}/`
- **Debug Mode**: Enable with `--debug` flag for verbose output
- **Agent Outputs**: Individual agent responses in `agent_outputs/`
- **Workspace Snapshots**: Agent working directories preserved
- **MCP Operations**: Tool calls and server interactions logged

## Important Notes

- The system is designed for **defensive security tasks only**
- Multi-agent coordination requires careful timeout management
- Claude Code agents provide the most comprehensive development capabilities
- MCP integration allows unlimited tool extensibility
- Workspace sharing enables complex collaborative workflows
- Configuration files in `massgen/configs/` provide ready-to-use examples

## Current Version: v0.0.17

Recent features include OpenAI MCP integration, uniform MCP architecture across backends, and enhanced debugging capabilities. See ROADMAP for upcoming features in v0.0.18.