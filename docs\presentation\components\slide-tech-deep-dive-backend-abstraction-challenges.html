        <!-- Slide 9: Tech Deep Dive - Backend Abstraction Challenges -->
        <div class="slide">
            <div class="icon">🔧</div>
            <h2>Tech Deep Dive: Backend Abstraction Challenges</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; align-items: center;">
                <div>
                    <ul style="font-size: 1.4em;">
                        <li><strong>🎭 Unified Interface:</strong> Standardized ChatAgent protocol for 8+ different backends</li>
                        <li><strong>🛠️ Tool Integration:</strong> Web search, code execution, MCP</li>
                        <li><strong>⚙️ StreamChunk Normalization:</strong> Convert diverse response formats to common protocol</li>
                        <li><strong>🔀 Backend-Specific Workarounds:</strong> Each provider has unique limitations</li>
                    </ul>
                </div>
                <div>
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 15px;">
                        <div style="font-size: 1.1em; font-weight: bold; margin-bottom: 15px; color: #2c3e50;">Backend Challenges:</div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; font-size: 0.9em;">
                            <div style="background: #fff3cd; padding: 10px; border-radius: 8px; text-align: center;">
                                <div style="font-weight: bold; color: #f39c12;">Claude Code CLI</div>
                                <div style="font-size: 0.8em; color: #666;">Context sharing across agents</div>
                            </div>
                            <div style="background: #ffebee; padding: 10px; border-radius: 8px; text-align: center;">
                                <div style="font-weight: bold; color: #e74c3c;">Gemini API</div>
                                <div style="font-size: 0.8em; color: #666;">Can't mix builtin + custom tools</div>
                            </div>
                            <div style="background: #e1f5fe; padding: 10px; border-radius: 8px; text-align: center;">
                                <div style="font-weight: bold; color: #0288d1;">GPT-5</div>
                                <div style="font-size: 0.8em; color: #666;">API change (reasoning, streaming etc.)</div>
                            </div>
                            <div style="background: #f3e5f5; padding: 10px; border-radius: 8px; text-align: center;">
                                <div style="font-weight: bold; color: #7b1fa2;">Most Backends</div>
                                <div style="font-size: 0.8em; color: #666;">Unable to autonomously collaborate</div>
                            </div>
                        </div>
                        <div style="margin-top: 15px; background: #e8f5e8; padding: 10px; border-radius: 8px; text-align: center;">
                            <div style="font-weight: bold; color: #27ae60;">🎯 Our Solution:</div>
                            <div style="font-size: 0.9em; color: #2d5a3d;">Binary Decision Framework & Advanced Workspace Sharing</div>
                        </div>
                    </div>
                    <div style="margin-top: 15px; font-size: 1em; color: #7f8c8d; text-align: center;">
                        <strong>Result:</strong> Unified interface with backend-specific optimizations
                    </div>
                </div>
            </div>
        </div>