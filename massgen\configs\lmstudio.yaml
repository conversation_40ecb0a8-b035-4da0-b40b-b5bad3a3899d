# MassGen Single Agent Configuration for LM Studio (local OpenAI-compatible server)]
# uv run python -m massgen.cli --config massgen/configs/lmstudio.yaml "tell me a joke about cats"
agent:
  id: "Qwen3-4b"
  backend:
    type: "lmstudio"
    model: "qwen/qwen3-4b"  # Replace with the model name LM Studio exposes
  system_message: "You are a helpful AI assistant running against a local LM Studio server."
ui:
  display_type: "rich_terminal"
  logging_enabled: true

